<template>
  <div class="login-container">
    <!-- 顶部图片区域 -->
    <div class="login-banner">
      <img src="@/assets/login/loginBg.png" alt="登录banner" />
    </div>

    <!-- 登录表单区域 -->
    <div class="login-form">
      <van-form @submit="onSubmit">
        <van-field
          v-model="loginForm.username"
          placeholder="请输入账号"
          :rules="[{ required: true, message: '请输入账号' }]"
        >
          <template #left-icon>
            <van-icon name="user-o" />
          </template>
        </van-field>

        <van-field
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <template #left-icon>
            <van-icon name="lock" />
          </template>
        </van-field>

        <van-field
          v-model="loginForm.code"
          placeholder="请输入验证码"
          :rules="[{ required: true, message: '请输入验证码' }]"
        >
          <template #left-icon>
            <van-icon name="shield-o" />
          </template>
          <template #right-icon>
            <img
              :src="codeUrl"
              class="captcha-img"
              @click="getCode"
              alt="验证码"
            />
          </template>
        </van-field>

        <!-- 记住密码选项 -->
        <div class="remember-password" v-if="isUniApp">
          <van-checkbox v-model="rememberPassword" icon-size="16px">
            记住密码
          </van-checkbox>
        </div>

        <div class="form-btn">
          <van-button round block type="info" native-type="submit">
            登录
          </van-button>
          <van-button round block style="margin-top: 16px" @click="ddLogin">
            一键登录
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
import { login, getCodeImg, getInfo } from "@/api/login";
import { setToken } from "@/util/auth";
import { saveLoginCredentials, getLoginCredentials } from "@/util/storage";
import dd from "gdt-jsapi";

export default {
  name: "Login",
  data() {
    return {
      loginForm: {
        username: "",
        password: "",
        code: "",
        uuid: "",
      },
      codeUrl: "",
      rememberPassword: false, // 记住密码选项
      isUniApp: false,
    };
  },
  created() {
    this.getCode();
    this.loadRememberedCredentials();
    if (navigator.userAgent.indexOf("uni-app") > -1) {
      console.log("运行在uni-app中");
      this.isUniApp = true;
    } else {
      console.log("不在uni-app中");
      this.isUniApp = false;
    }
  },
  methods: {
    async getCode() {
      const res = await getCodeImg();
      this.codeUrl = "data:image/gif;base64," + res.img;
      this.loginForm.uuid = res.uuid;
    },
    async onSubmit() {
      login(this.loginForm)
        .then((res) => {
          // 登录成功后保存或清除记住的密码
          saveLoginCredentials(
            {
              username: this.loginForm.username,
              password: this.loginForm.password,
            },
            this.rememberPassword
          );

          setToken(res.token);
          this.GetInfo(res.token);
          this.$toast.success("登录成功");
          this.$router.push("/");
        })
        .catch((error) => {
          this.$toast.fail(error.msg);
          // 登录失败时刷新验证码
          this.getCode();
        });
    },
    ddLogin() {
      console.log("kaishi");
      dd.getAuthCode({}).then(
        (result) => {
          if (result) {
            login({ authCode: result.code })
              .then((res) => {
                setToken(res.token);
                this.GetInfo(res.token);
                this.$toast.success("登录成功");
                this.$router.push("/");
              })
              .catch((error) => {
                this.$toast.fail(error.msg);
                // 登录失败时刷新验证码
                this.getCode();
              });
          }
        },
        (err) => {
          console.log(err);
        }
      );
    },
    GetInfo(token) {
      getInfo(token).then((res) => {
        const user = res.user;
        const userObj = {
          deptName: user?.dept?.deptName || "",
          deptId: user?.dept?.deptId || "",
          nickName: user.nickName,
          ...user,
          id: res?.dogUserQualifi?.id || "",
          realName: user.userName || "",
          mobile: user.phonenumber || "",
          userQualifi: res?.dogUserQualifi || undefined,
          qualifi: res?.dogQualifi || undefined,
          roleType: res.roleType,
          roles: res.roles,
          userType: user.userType,
          userId: user.userId,
        };
        this.vuex("user", userObj);

        // 初始化全局定位管理器
        this.$globalLocationManager.init({
          userId: userObj.userId,
          userType: userObj.userType,
          userName: userObj.nickName || userObj.realName,
        });
      });
    },
    // 加载记住的登录凭据
    loadRememberedCredentials() {
      try {
        const credentials = getLoginCredentials();
        if (credentials) {
          this.loginForm.username = credentials.username;
          this.loginForm.password = credentials.password;
          this.rememberPassword = credentials.remember;
        }
      } catch (error) {
        console.error("加载记住的密码失败:", error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.login-container {
  min-height: 100vh;
  background: #fff;
}

.login-banner {
  position: relative;
  text-align: center;
  padding: 8vh 0 4vh;
}

.login-banner img {
  width: 100%;
  max-width: 90vw;
  max-height: 30vh;
  object-fit: contain;
}

.login-form {
  padding: 2vh 2.5vw;
  max-width: 90%;
  margin: 0 auto;
}

.remember-password {
  margin: 2vh 0 1vh;
  display: flex;
  align-items: center;
}

.form-btn {
  margin-top: 2vh;
}

:deep(.van-field__left-icon) {
  margin-right: 1.5vw;
  display: flex;
  justify-content: center;
  align-items: center;
  .van-icon {
    font-size: 15px;
  }
}

:deep(.van-field__error-message) {
  font-size: 0;
  height: 0;
}

.captcha-img {
  height: 36px;
  cursor: pointer;
}

:deep(.van-field) {
  margin-bottom: 1vh;
  font-size: 15px;
}

:deep(.van-button) {
  height: 50px;
  font-size: 16.5px;
}
</style>
