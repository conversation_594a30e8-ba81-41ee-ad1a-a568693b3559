/**
 * 统一存储工具类
 * 支持uniapp和web环境
 */

// 检测是否在uniapp环境中
const isUniApp = typeof uni !== 'undefined' && uni.setStorage

/**
 * 设置存储
 * @param {string} key 存储键
 * @param {any} value 存储值
 * @param {boolean} sync 是否同步存储，默认false
 */
export function setStorage(key, value, sync = false) {
  try {
    const data = JSON.stringify(value)
    
    if (isUniApp) {
      if (sync) {
        uni.setStorageSync(key, data)
      } else {
        uni.setStorage({
          key: key,
          data: data,
          success: function () {
            console.log('存储成功')
          },
          fail: function (err) {
            console.error('存储失败:', err)
          }
        })
      }
    } else {
      // web环境使用localStorage
      localStorage.setItem(key, data)
    }
  } catch (error) {
    console.error('setStorage error:', error)
  }
}

/**
 * 获取存储
 * @param {string} key 存储键
 * @param {boolean} sync 是否同步获取，默认false
 * @returns {Promise|any} 存储值
 */
export function getStorage(key, sync = false) {
  try {
    if (isUniApp) {
      if (sync) {
        const data = uni.getStorageSync(key)
        return data ? JSON.parse(data) : null
      } else {
        return new Promise((resolve, reject) => {
          uni.getStorage({
            key: key,
            success: function (res) {
              try {
                const data = JSON.parse(res.data)
                resolve(data)
              } catch (error) {
                resolve(null)
              }
            },
            fail: function (err) {
              resolve(null)
            }
          })
        })
      }
    } else {
      // web环境使用localStorage
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : null
    }
  } catch (error) {
    console.error('getStorage error:', error)
    return sync ? null : Promise.resolve(null)
  }
}

/**
 * 移除存储
 * @param {string} key 存储键
 * @param {boolean} sync 是否同步移除，默认false
 */
export function removeStorage(key, sync = false) {
  try {
    if (isUniApp) {
      if (sync) {
        uni.removeStorageSync(key)
      } else {
        uni.removeStorage({
          key: key,
          success: function () {
            console.log('移除成功')
          },
          fail: function (err) {
            console.error('移除失败:', err)
          }
        })
      }
    } else {
      // web环境使用localStorage
      localStorage.removeItem(key)
    }
  } catch (error) {
    console.error('removeStorage error:', error)
  }
}

/**
 * 清空所有存储
 * @param {boolean} sync 是否同步清空，默认false
 */
export function clearStorage(sync = false) {
  try {
    if (isUniApp) {
      if (sync) {
        uni.clearStorageSync()
      } else {
        uni.clearStorage({
          success: function () {
            console.log('清空成功')
          },
          fail: function (err) {
            console.error('清空失败:', err)
          }
        })
      }
    } else {
      // web环境使用localStorage
      localStorage.clear()
    }
  } catch (error) {
    console.error('clearStorage error:', error)
  }
}

// 记住密码相关的存储键
export const STORAGE_KEYS = {
  REMEMBER_PASSWORD: 'remember_password_info',
  LOGIN_CREDENTIALS: 'login_credentials'
}

/**
 * 保存记住密码信息
 * @param {Object} credentials 登录凭据
 * @param {string} credentials.username 用户名
 * @param {string} credentials.password 密码
 * @param {boolean} remember 是否记住密码
 */
export function saveLoginCredentials(credentials, remember = false) {
  if (remember) {
    setStorage(STORAGE_KEYS.LOGIN_CREDENTIALS, {
      username: credentials.username,
      password: credentials.password,
      remember: true,
      saveTime: new Date().getTime()
    }, true)
  } else {
    removeStorage(STORAGE_KEYS.LOGIN_CREDENTIALS, true)
  }
}

/**
 * 获取记住的登录凭据
 * @returns {Object|null} 登录凭据或null
 */
export function getLoginCredentials() {
  try {
    const credentials = getStorage(STORAGE_KEYS.LOGIN_CREDENTIALS, true)
    if (credentials && credentials.remember) {
      // 检查是否过期（30天）
      const now = new Date().getTime()
      const saveTime = credentials.saveTime || 0
      const expireTime = 30 * 24 * 60 * 60 * 1000 // 30天
      
      if (now - saveTime > expireTime) {
        // 已过期，清除存储
        removeStorage(STORAGE_KEYS.LOGIN_CREDENTIALS, true)
        return null
      }
      
      return {
        username: credentials.username || '',
        password: credentials.password || '',
        remember: true
      }
    }
    return null
  } catch (error) {
    console.error('getLoginCredentials error:', error)
    return null
  }
}

/**
 * 清除记住的登录凭据
 */
export function clearLoginCredentials() {
  removeStorage(STORAGE_KEYS.LOGIN_CREDENTIALS, true)
}
